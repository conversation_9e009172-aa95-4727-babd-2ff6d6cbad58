import React from 'react';
import { render, screen } from '@testing-library/react';
import { describe, test, expect } from 'vitest';
import { Avatar } from '../avatar';

describe('Avatar Component - Circle Bug Fix', () => {
  test('displays initials when name is provided', () => {
    render(
      <Avatar name="<PERSON>. <PERSON>" data-testid="avatar-with-name" />
    );
    
    const avatar = screen.getByTestId('avatar-with-name');
    expect(avatar).toBeInTheDocument();
    
    // Should show initials "DC" for "<PERSON>. <PERSON>"
    expect(avatar).toHaveTextContent('DC');
  });

  test('displays initials for single name', () => {
    render(
      <Avatar name="<PERSON>" data-testid="avatar-single-name" />
    );
    
    const avatar = screen.getByTestId('avatar-single-name');
    expect(avatar).toBeInTheDocument();
    
    // Should show initial "J" for "<PERSON>"
    expect(avatar).toHaveTextContent('J');
  });

  test('displays initials for multiple names', () => {
    render(
      <Avatar name="<PERSON><PERSON>" data-testid="avatar-multiple-names" />
    );
    
    const avatar = screen.getByTestId('avatar-multiple-names');
    expect(avatar).toBeInTheDocument();
    
    // Should show "PP" (first and last name initials)
    expect(avatar).toHaveTextContent('PP');
  });

  test('does not show empty circle when name is provided', () => {
    render(
      <Avatar name="John Kumar" data-testid="avatar-no-empty-circle" />
    );
    
    const avatar = screen.getByTestId('avatar-no-empty-circle');
    
    // Should not have the empty circle fallback
    const emptyCircle = avatar.querySelector('.h-4.w-4.rounded-full.bg-muted-foreground\\/20');
    expect(emptyCircle).toBeNull();
    
    // Should show proper initials
    expect(avatar).toHaveTextContent('JK');
  });

  test('shows empty circle only when no name is provided', () => {
    render(
      <Avatar data-testid="avatar-no-name" />
    );
    
    const avatar = screen.getByTestId('avatar-no-name');
    expect(avatar).toBeInTheDocument();
    
    // Should have the empty circle fallback when no name
    const emptyCircle = avatar.querySelector('div[class*="h-4"][class*="w-4"][class*="rounded-full"]');
    expect(emptyCircle).toBeInTheDocument();
  });

  test('works with image src and name fallback', () => {
    render(
      <Avatar 
        src="https://example.com/avatar.jpg" 
        name="Sarah Chen" 
        data-testid="avatar-with-src" 
      />
    );
    
    const avatar = screen.getByTestId('avatar-with-src');
    expect(avatar).toBeInTheDocument();
    
    // Should have an img element
    const img = avatar.querySelector('img');
    expect(img).toBeInTheDocument();
    expect(img).toHaveAttribute('src', 'https://example.com/avatar.jpg');
  });
});
